<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Java开发者简历</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            line-height: 1.6;
            color: #333;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 20px;
        }

        .container {
            max-width: 1000px;
            margin: 0 auto;
            background: white;
            border-radius: 15px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            overflow: hidden;
        }

        .header {
            background: linear-gradient(135deg, #2c3e50 0%, #3498db 100%);
            color: white;
            padding: 40px;
            text-align: center;
            position: relative;
        }

        .header::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grain" width="100" height="100" patternUnits="userSpaceOnUse"><circle cx="25" cy="25" r="1" fill="rgba(255,255,255,0.1)"/><circle cx="75" cy="75" r="1" fill="rgba(255,255,255,0.1)"/><circle cx="50" cy="10" r="0.5" fill="rgba(255,255,255,0.1)"/></pattern></defs><rect width="100" height="100" fill="url(%23grain)"/></svg>');
            opacity: 0.3;
        }

        .profile-img {
            width: 120px;
            height: 120px;
            border-radius: 50%;
            border: 4px solid white;
            margin: 0 auto 20px;
            background: #ecf0f1;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 48px;
            color: #3498db;
            position: relative;
            z-index: 1;
        }

        .name {
            font-size: 2.5em;
            font-weight: 300;
            margin-bottom: 10px;
            position: relative;
            z-index: 1;
        }

        .title {
            font-size: 1.3em;
            opacity: 0.9;
            margin-bottom: 20px;
            position: relative;
            z-index: 1;
        }

        .contact-info {
            display: flex;
            justify-content: center;
            gap: 30px;
            flex-wrap: wrap;
            position: relative;
            z-index: 1;
        }

        .contact-item {
            display: flex;
            align-items: center;
            gap: 8px;
            font-size: 0.95em;
        }

        .main-content {
            padding: 40px;
        }

        .content {
            max-width: 100%;
        }

        .section {
            margin-bottom: 35px;
        }

        .section-title {
            font-size: 1.4em;
            color: #2c3e50;
            margin-bottom: 20px;
            padding-bottom: 10px;
            border-bottom: 2px solid #3498db;
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .skills-tags {
            display: flex;
            flex-wrap: wrap;
            gap: 10px;
        }

        .skill-tag-item {
            display: inline-flex;
            align-items: center;
            gap: 6px;
            padding: 8px 12px;
            border-radius: 20px;
            font-size: 0.9em;
            font-weight: 500;
            transition: all 0.3s ease;
            cursor: default;
        }

        .skill-tag-item:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(0,0,0,0.15);
        }

        .skill-tag-item i {
            font-size: 1em;
        }

        .skill-tag-item.expert {
            background: linear-gradient(135deg, #27ae60, #2ecc71);
            color: white;
            border: 2px solid #27ae60;
        }

        .skill-tag-item.proficient {
            background: linear-gradient(135deg, #3498db, #5dade2);
            color: white;
            border: 2px solid #3498db;
        }

        .skill-tag-item.intermediate {
            background: linear-gradient(135deg, #f39c12, #f7dc6f);
            color: white;
            border: 2px solid #f39c12;
        }

        .experience-item, .education-item {
            margin-bottom: 25px;
            padding: 20px;
            background: #f8f9fa;
            border-radius: 8px;
            border-left: 4px solid #3498db;
        }

        .item-header {
            display: flex;
            justify-content: space-between;
            align-items: flex-start;
            margin-bottom: 10px;
        }

        .item-title {
            font-weight: 600;
            color: #2c3e50;
            font-size: 1.1em;
        }

        .item-company {
            color: #3498db;
            font-weight: 500;
        }

        .item-date {
            color: #7f8c8d;
            font-size: 0.9em;
            background: white;
            padding: 4px 12px;
            border-radius: 15px;
        }

        .item-description {
            color: #555;
            line-height: 1.6;
        }



        @media (max-width: 768px) {
            .contact-info {
                flex-direction: column;
                gap: 15px;
            }

            .item-header {
                flex-direction: column;
                gap: 10px;
            }

            .main-content {
                padding: 20px;
            }
        }

        .highlight {
            background: linear-gradient(120deg, #a8edea 0%, #fed6e3 100%);
            padding: 2px 6px;
            border-radius: 4px;
            font-weight: 500;
        }

        .advantages-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
            gap: 20px;
            margin-top: 20px;
        }

        .advantage-item {
            display: flex;
            align-items: flex-start;
            gap: 15px;
            padding: 20px;
            background: #f8f9fa;
            border-radius: 10px;
            border: 1px solid #e9ecef;
            transition: all 0.3s ease;
        }

        .advantage-item:hover {
            transform: translateY(-3px);
            box-shadow: 0 8px 25px rgba(0,0,0,0.1);
            border-color: #3498db;
        }

        .advantage-icon {
            width: 50px;
            height: 50px;
            background: linear-gradient(135deg, #3498db, #2980b9);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 1.3em;
            flex-shrink: 0;
        }

        .advantage-content h3 {
            color: #2c3e50;
            font-size: 1.1em;
            margin-bottom: 8px;
            font-weight: 600;
        }

        .advantage-content p {
            color: #555;
            line-height: 1.5;
            font-size: 0.95em;
        }
    </style>
</head>
<body>
    <div class="container">
        <header class="header">
            <div class="profile-img">
                <i class="fas fa-user"></i>
            </div>
            <h1 class="name">张三</h1>
            <p class="title">高级Java开发工程师</p>
            <div class="contact-info">
                <div class="contact-item">
                    <i class="fas fa-envelope"></i>
                    <span><EMAIL></span>
                </div>
                <div class="contact-item">
                    <i class="fas fa-phone"></i>
                    <span>138-0000-0000</span>
                </div>
                <div class="contact-item">
                    <i class="fab fa-github"></i>
                    <span>github.com/zhangsan</span>
                </div>
                <div class="contact-item">
                    <i class="fas fa-map-marker-alt"></i>
                    <span>北京市</span>
                </div>
            </div>
        </header>

        <div class="main-content">
                <section class="section">
                    <h2 class="section-title">
                        <i class="fas fa-user-circle"></i>
                        个人简介
                    </h2>
                    <p>具有<span class="highlight">5年+</span>Java开发经验的高级工程师，熟练掌握Spring生态系统，具备丰富的<span class="highlight">微服务架构</span>设计和实施经验。擅长高并发系统设计，对性能优化有深入理解。具备良好的团队协作能力和技术领导力。</p>
                </section>

                <section class="section">
                    <h2 class="section-title">
                        <i class="fas fa-code"></i>
                        技术技能
                    </h2>
                    <div class="skills-tags">
                        <!-- 后端技术 -->
                        <span class="skill-tag-item expert">
                            <i class="fab fa-java"></i>
                            Java
                        </span>
                        <span class="skill-tag-item proficient">
                            <i class="fas fa-leaf"></i>
                            Spring Boot
                        </span>
                        <span class="skill-tag-item proficient">
                            <i class="fas fa-cloud"></i>
                            Spring Cloud
                        </span>
                        <span class="skill-tag-item proficient">
                            <i class="fas fa-database"></i>
                            MySQL
                        </span>
                        <span class="skill-tag-item intermediate">
                            <i class="fas fa-memory"></i>
                            Redis
                        </span>
                        <span class="skill-tag-item intermediate">
                            <i class="fab fa-docker"></i>
                            Docker
                        </span>
                        <span class="skill-tag-item proficient">
                            <i class="fas fa-exchange-alt"></i>
                            RabbitMQ
                        </span>
                        <span class="skill-tag-item intermediate">
                            <i class="fab fa-aws"></i>
                            微服务
                        </span>

                        <!-- 前端技术 -->
                        <span class="skill-tag-item proficient">
                            <i class="fab fa-vuejs"></i>
                            Vue.js
                        </span>
                        <span class="skill-tag-item intermediate">
                            <i class="fab fa-js-square"></i>
                            jQuery
                        </span>
                        <span class="skill-tag-item proficient">
                            <i class="fab fa-html5"></i>
                            HTML5
                        </span>
                        <span class="skill-tag-item proficient">
                            <i class="fab fa-css3-alt"></i>
                            CSS3
                        </span>
                        <span class="skill-tag-item intermediate">
                            <i class="fab fa-js"></i>
                            JavaScript
                        </span>
                        <span class="skill-tag-item intermediate">
                            <i class="fab fa-bootstrap"></i>
                            Bootstrap
                        </span>

                        <!-- 工具技术 -->
                        <span class="skill-tag-item proficient">
                            <i class="fas fa-code-branch"></i>
                            Git
                        </span>
                        <span class="skill-tag-item intermediate">
                            <i class="fab fa-linux"></i>
                            Linux
                        </span>
                        <span class="skill-tag-item proficient">
                            <i class="fas fa-server"></i>
                            Nginx
                        </span>
                    </div>
                </section>

                <section class="section">
                    <h2 class="section-title">
                        <i class="fas fa-briefcase"></i>
                        工作经历
                    </h2>
                    <div class="experience-item">
                        <div class="item-header">
                            <div>
                                <div class="item-title">高级Java开发工程师</div>
                                <div class="item-company">重庆数子引力网络科技有限公司</div>
                            </div>
                            <div class="item-date">2021.03 - 至今</div>
                        </div>
                        <div class="item-description">
                            • 负责公司核心业务系统的架构设计和开发，支撑日均千万级请求<br>
                            • 主导微服务架构改造，将单体应用拆分为20+个微服务，提升系统可维护性<br>
                            • 优化数据库查询性能，将关键接口响应时间从2s降低到200ms<br>
                            • 带领5人开发团队，负责技术方案评审和代码质量把控
                        </div>
                    </div>
                    <div class="experience-item">
                        <div class="item-header">
                            <div>
                                <div class="item-title">全栈开发工程师</div>
                                <div class="item-company">重庆锐云科技有限公司</div>
                            </div>
                            <div class="item-date">2019.06 - 2021.02</div>
                        </div>
                        <div class="item-description">
                            • 参与电商平台后端系统开发，负责订单、支付等核心模块<br>
                            • 使用Spring Boot + MyBatis构建RESTful API，支撑前端和移动端<br>
                            • 集成第三方支付系统，实现多种支付方式的统一处理<br>
                            • 参与系统监控和日志分析，及时发现和解决线上问题
                        </div>
                    </div>
                    <div class="experience-item">
                        <div class="item-header">
                            <div>
                                <div class="item-title">Java开发工程师</div>
                                <div class="item-company">重庆满集网络科技有限公司</div>
                            </div>
                            <div class="item-date">2019.06 - 2021.02</div>
                        </div>
                        <div class="item-description">
                            • 参与电商平台后端系统开发，负责订单、支付等核心模块<br>
                            • 使用Spring Boot + MyBatis构建RESTful API，支撑前端和移动端<br>
                            • 集成第三方支付系统，实现多种支付方式的统一处理<br>
                            • 参与系统监控和日志分析，及时发现和解决线上问题
                        </div>
                    </div>
                    <div class="experience-item">
                        <div class="item-header">
                            <div>
                                <div class="item-title">Java开发工程师</div>
                                <div class="item-company">重庆迪斯马森科技有限公司</div>
                            </div>
                            <div class="item-date">2019.06 - 2021.02</div>
                        </div>
                        <div class="item-description">
                            • 参与电商平台后端系统开发，负责订单、支付等核心模块<br>
                            • 使用Spring Boot + MyBatis构建RESTful API，支撑前端和移动端<br>
                            • 集成第三方支付系统，实现多种支付方式的统一处理<br>
                            • 参与系统监控和日志分析，及时发现和解决线上问题
                        </div>
                    </div>  
                </section>

                <section class="section">
                    <h2 class="section-title">
                        <i class="fas fa-graduation-cap"></i>
                        教育背景
                    </h2>
                    <div class="education-item">
                        <div class="item-header">
                            <div>
                                <div class="item-title">软件技术专业</div>
                                <div class="item-company">重庆工商职业技术学院</div>
                            </div>
                            <div class="item-date">2013.08 - 2016.06</div>
                        </div>
                        <div class="item-description">
                            主修课程：Java编程、C语言、 C++、操作系统、计算机网络
                        </div>
                    </div>
                </section>

                <section class="section">
                    <h2 class="section-title">
                        <i class="fas fa-project-diagram"></i>
                        项目经验
                    </h2>
                    <div class="experience-item">
                        <div class="item-header">
                            <div>
                                <div class="item-title">宗申摩托-国际智慧营销平台</div>
                                <div class="item-company">技术栈：Spring Boot, Vue.js, MySQL, Redis, Docker</div>
                            </div>
                            <div class="item-date">2024.04 - 2025.06</div>
                        </div>
                        <div class="item-description">
                            • 负责宗申摩托国际智慧营销平台的核心功能开发，支撑全球经销商业务<br>
                            • 使用Spring Boot构建微服务架构，实现用户管理、订单处理、库存管理等核心模块<br>
                            • 前端采用Vue.js开发响应式界面，提供良好的用户体验<br>
                            • 集成Redis缓存优化查询性能，使用MySQL进行数据持久化<br>
                            • 通过Docker容器化部署，提高系统的可维护性和扩展性<br>
                            • 系统支持多语言、多币种，服务于全球20+个国家的经销商网络
                        </div>
                    </div>
                    <div class="experience-item">
                        <div class="item-header">
                            <div>
                                <div class="item-title">分布式电商平台</div>
                                <div class="item-company">技术栈：Spring Cloud, MySQL, Redis, RabbitMQ</div>
                            </div>
                            <div class="item-date">2022.01 - 2022.12</div>
                        </div>
                        <div class="item-description">
                            • 设计并实现了支持高并发的分布式电商系统<br>
                            • 使用Spring Cloud Gateway作为API网关，实现统一鉴权和限流<br>
                            • 采用Redis缓存热点数据，RabbitMQ处理异步消息<br>
                            • 系统支持日均100万订单处理，峰值QPS达到5000+
                        </div>
                    </div>
                </section>

                <section class="section">
                    <h2 class="section-title">
                        <i class="fas fa-star"></i>
                        我的优势
                    </h2>
                    <div class="advantages-grid">
                        <div class="advantage-item">
                            <div class="advantage-icon">
                                <i class="fas fa-rocket"></i>
                            </div>
                            <div class="advantage-content">
                                <h3>技术深度</h3>
                                <p>深入理解Java底层原理，熟悉JVM调优，具备扎实的计算机基础知识</p>
                            </div>
                        </div>
                        <div class="advantage-item">
                            <div class="advantage-icon">
                                <i class="fas fa-users"></i>
                            </div>
                            <div class="advantage-content">
                                <h3>团队协作</h3>
                                <p>积极配合成员工作，有效沟通、合理分工、关注进度，确保团队目标达成。</p>
                            </div>
                        </div>
                        <div class="advantage-item">
                            <div class="advantage-icon">
                                <i class="fas fa-lightbulb"></i>
                            </div>
                            <div class="advantage-content">
                                <h3>问题解决</h3>
                                <p>善于分析复杂问题，能够快速定位并解决线上故障，保障系统稳定运行</p>
                            </div>
                        </div>
                        <div class="advantage-item">
                            <div class="advantage-icon">
                                <i class="fas fa-chart-line"></i>
                            </div>
                            <div class="advantage-content">
                                <h3>性能优化</h3>
                                <p>擅长系统性能调优，有丰富的高并发场景处理经验</p>
                            </div>
                        </div>
                    </div>
                </section>
        </div>
    </div>
</body>
</html>
