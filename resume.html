<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Java开发者简历</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            line-height: 1.6;
            color: #333;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 20px;
        }

        .container {
            max-width: 1000px;
            margin: 0 auto;
            background: white;
            border-radius: 15px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            overflow: hidden;
        }

        .header {
            background: linear-gradient(135deg, #2c3e50 0%, #3498db 100%);
            color: white;
            padding: 40px;
            text-align: center;
            position: relative;
        }

        .header::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grain" width="100" height="100" patternUnits="userSpaceOnUse"><circle cx="25" cy="25" r="1" fill="rgba(255,255,255,0.1)"/><circle cx="75" cy="75" r="1" fill="rgba(255,255,255,0.1)"/><circle cx="50" cy="10" r="0.5" fill="rgba(255,255,255,0.1)"/></pattern></defs><rect width="100" height="100" fill="url(%23grain)"/></svg>');
            opacity: 0.3;
        }

        .profile-img {
            width: 120px;
            height: 120px;
            border-radius: 50%;
            border: 4px solid white;
            margin: 0 auto 20px;
            background: #ecf0f1;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 48px;
            color: #3498db;
            position: relative;
            z-index: 1;
        }

        .name {
            font-size: 2.5em;
            font-weight: 300;
            margin-bottom: 10px;
            position: relative;
            z-index: 1;
        }

        .title {
            font-size: 1.3em;
            opacity: 0.9;
            margin-bottom: 20px;
            position: relative;
            z-index: 1;
        }

        .contact-info {
            display: flex;
            justify-content: center;
            gap: 30px;
            flex-wrap: wrap;
            position: relative;
            z-index: 1;
        }

        .contact-item {
            display: flex;
            align-items: center;
            gap: 8px;
            font-size: 0.95em;
        }

        .main-content {
            padding: 40px;
        }

        .content {
            max-width: 100%;
        }

        .section {
            margin-bottom: 35px;
        }

        .section-title {
            font-size: 1.4em;
            color: #2c3e50;
            margin-bottom: 20px;
            padding-bottom: 10px;
            border-bottom: 2px solid #3498db;
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .skills-tags {
            display: flex;
            flex-wrap: wrap;
            gap: 10px;
        }

        .skill-tag-item {
            display: inline-flex;
            align-items: center;
            gap: 6px;
            padding: 8px 12px;
            border-radius: 20px;
            font-size: 0.9em;
            font-weight: 500;
            transition: all 0.3s ease;
            cursor: default;
        }

        .skill-tag-item:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(0,0,0,0.15);
        }

        .skill-tag-item i {
            font-size: 1em;
        }

        .skill-tag-item.expert {
            background: linear-gradient(135deg, #27ae60, #2ecc71);
            color: white;
            border: 2px solid #27ae60;
        }

        .skill-tag-item.proficient {
            background: linear-gradient(135deg, #3498db, #5dade2);
            color: white;
            border: 2px solid #3498db;
        }

        .skill-tag-item.intermediate {
            background: linear-gradient(135deg, #f39c12, #f7dc6f);
            color: white;
            border: 2px solid #f39c12;
        }

        .experience-item, .education-item {
            margin-bottom: 25px;
            padding: 20px;
            background: #f8f9fa;
            border-radius: 8px;
            border-left: 4px solid #3498db;
        }

        .item-header {
            display: flex;
            justify-content: space-between;
            align-items: flex-start;
            margin-bottom: 10px;
        }

        .item-title {
            font-weight: 600;
            color: #2c3e50;
            font-size: 1.1em;
        }

        .item-company {
            color: #3498db;
            font-weight: 500;
        }

        .item-date {
            color: #7f8c8d;
            font-size: 0.9em;
            background: white;
            padding: 4px 12px;
            border-radius: 15px;
        }

        .item-description {
            color: #555;
            line-height: 1.6;
        }



        @media (max-width: 768px) {
            .contact-info {
                flex-direction: column;
                gap: 15px;
            }

            .item-header {
                flex-direction: column;
                gap: 10px;
            }

            .main-content {
                padding: 20px;
            }
        }

        .highlight {
            background: linear-gradient(120deg, #a8edea 0%, #fed6e3 100%);
            padding: 2px 6px;
            border-radius: 4px;
            font-weight: 500;
        }

        .advantages-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
            gap: 20px;
            margin-top: 20px;
        }

        .advantage-item {
            display: flex;
            align-items: flex-start;
            gap: 15px;
            padding: 20px;
            background: #f8f9fa;
            border-radius: 10px;
            border: 1px solid #e9ecef;
            transition: all 0.3s ease;
        }

        .advantage-item:hover {
            transform: translateY(-3px);
            box-shadow: 0 8px 25px rgba(0,0,0,0.1);
            border-color: #3498db;
        }

        .advantage-icon {
            width: 50px;
            height: 50px;
            background: linear-gradient(135deg, #3498db, #2980b9);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 1.3em;
            flex-shrink: 0;
        }

        .advantage-content h3 {
            color: #2c3e50;
            font-size: 1.1em;
            margin-bottom: 8px;
            font-weight: 600;
        }

        .advantage-content p {
            color: #555;
            line-height: 1.5;
            font-size: 0.95em;
        }
    </style>
</head>
<body>
    <div class="container">
        <header class="header">
            <div class="profile-img">
                <i class="fas fa-user"></i>
            </div>
            <h1 class="name">张三</h1>
            <p class="title">高级Java开发工程师</p>
            <div class="contact-info">
                <div class="contact-item">
                    <i class="fas fa-envelope"></i>
                    <span><EMAIL></span>
                </div>
                <div class="contact-item">
                    <i class="fas fa-phone"></i>
                    <span>138-0000-0000</span>
                </div>
                <div class="contact-item">
                    <i class="fab fa-github"></i>
                    <span>github.com/zhangsan</span>
                </div>
                <div class="contact-item">
                    <i class="fas fa-map-marker-alt"></i>
                    <span>北京市</span>
                </div>
            </div>
        </header>

        <div class="main-content">
                <section class="section">
                    <h2 class="section-title">
                        <i class="fas fa-user-circle"></i>
                        个人简介
                    </h2>
                    <p>具有<span class="highlight">5年+</span>Java开发经验的高级工程师，熟练掌握Spring生态系统，具备丰富的<span class="highlight">微服务架构</span>设计和实施经验。擅长高并发系统设计，对性能优化有深入理解。具备良好的团队协作能力和技术领导力。</p>
                </section>

                <section class="section">
                    <h2 class="section-title">
                        <i class="fas fa-code"></i>
                        技术技能
                    </h2>
                    <div class="skills-tags">
                        <!-- 后端技术 -->
                        <span class="skill-tag-item expert">
                            <i class="fab fa-java"></i>
                            Java
                        </span>
                        <span class="skill-tag-item proficient">
                            <i class="fas fa-leaf"></i>
                            Spring Boot
                        </span>
                        <span class="skill-tag-item proficient">
                            <i class="fas fa-cloud"></i>
                            Spring Cloud
                        </span>
                        <span class="skill-tag-item proficient">
                            <i class="fas fa-database"></i>
                            MySQL
                        </span>
                        <span class="skill-tag-item intermediate">
                            <i class="fas fa-memory"></i>
                            Redis
                        </span>
                        <span class="skill-tag-item intermediate">
                            <i class="fab fa-docker"></i>
                            Docker
                        </span>
                        <span class="skill-tag-item proficient">
                            <i class="fas fa-exchange-alt"></i>
                            RabbitMQ
                        </span>
                        <span class="skill-tag-item intermediate">
                            <i class="fab fa-aws"></i>
                            微服务
                        </span>

                        <!-- 前端技术 -->
                        <span class="skill-tag-item proficient">
                            <i class="fab fa-vuejs"></i>
                            Vue.js
                        </span>
                        <span class="skill-tag-item intermediate">
                            <i class="fab fa-js-square"></i>
                            jQuery
                        </span>
                        <span class="skill-tag-item proficient">
                            <i class="fab fa-html5"></i>
                            HTML5
                        </span>
                        <span class="skill-tag-item proficient">
                            <i class="fab fa-css3-alt"></i>
                            CSS3
                        </span>
                        <span class="skill-tag-item intermediate">
                            <i class="fab fa-js"></i>
                            JavaScript
                        </span>
                        <span class="skill-tag-item intermediate">
                            <i class="fab fa-bootstrap"></i>
                            Bootstrap
                        </span>

                        <!-- 工具技术 -->
                        <span class="skill-tag-item proficient">
                            <i class="fas fa-code-branch"></i>
                            Git
                        </span>
                        <span class="skill-tag-item intermediate">
                            <i class="fab fa-linux"></i>
                            Linux
                        </span>
                        <span class="skill-tag-item proficient">
                            <i class="fas fa-server"></i>
                            Nginx
                        </span>
                    </div>
                </section>

                <section class="section">
                    <h2 class="section-title">
                        <i class="fas fa-briefcase"></i>
                        工作经历
                    </h2>
                    <div class="experience-item">
                        <div class="item-header">
                            <div>
                                <div class="item-title">高级Java开发工程师</div>
                                <div class="item-company">重庆数子引力网络科技有限公司</div>
                            </div>
                            <div class="item-date">2021.03 - 至今</div>
                        </div>
                        <div class="item-description">
                            • 负责公司核心业务系统的架构设计和开发，支撑日均千万级请求<br>
                            • 主导微服务架构改造，将单体应用拆分为20+个微服务，提升系统可维护性<br>
                            • 优化数据库查询性能，将关键接口响应时间从2s降低到200ms<br>
                            • 带领5人开发团队，负责技术方案评审和代码质量把控
                        </div>
                    </div>
                    <div class="experience-item">
                        <div class="item-header">
                            <div>
                                <div class="item-title">全栈开发工程师</div>
                                <div class="item-company">重庆锐云科技有限公司</div>
                            </div>
                            <div class="item-date">2019.06 - 2021.02</div>
                        </div>
                        <div class="item-description">
                            • 参与电商平台后端系统开发，负责订单、支付等核心模块<br>
                            • 使用Spring Boot + MyBatis构建RESTful API，支撑前端和移动端<br>
                            • 集成第三方支付系统，实现多种支付方式的统一处理<br>
                            • 参与系统监控和日志分析，及时发现和解决线上问题
                        </div>
                    </div>
                    <div class="experience-item">
                        <div class="item-header">
                            <div>
                                <div class="item-title">Java开发工程师</div>
                                <div class="item-company">重庆满集网络科技有限公司</div>
                            </div>
                            <div class="item-date">2019.06 - 2021.02</div>
                        </div>
                        <div class="item-description">
                            • 参与电商平台后端系统开发，负责订单、支付等核心模块<br>
                            • 使用Spring Boot + MyBatis构建RESTful API，支撑前端和移动端<br>
                            • 集成第三方支付系统，实现多种支付方式的统一处理<br>
                            • 参与系统监控和日志分析，及时发现和解决线上问题
                        </div>
                    </div>
                    <div class="experience-item">
                        <div class="item-header">
                            <div>
                                <div class="item-title">Java开发工程师</div>
                                <div class="item-company">重庆迪斯马森科技有限公司</div>
                            </div>
                            <div class="item-date">2019.06 - 2021.02</div>
                        </div>
                        <div class="item-description">
                            • 参与电商平台后端系统开发，负责订单、支付等核心模块<br>
                            • 使用Spring Boot + MyBatis构建RESTful API，支撑前端和移动端<br>
                            • 集成第三方支付系统，实现多种支付方式的统一处理<br>
                            • 参与系统监控和日志分析，及时发现和解决线上问题
                        </div>
                    </div>  
                </section>

                <section class="section">
                    <h2 class="section-title">
                        <i class="fas fa-graduation-cap"></i>
                        教育背景
                    </h2>
                    <div class="education-item">
                        <div class="item-header">
                            <div>
                                <div class="item-title">软件技术专业</div>
                                <div class="item-company">重庆工商职业技术学院</div>
                            </div>
                            <div class="item-date">2013.08 - 2016.06</div>
                        </div>
                        <div class="item-description">
                            主修课程：Java编程、C语言、 C++、操作系统、计算机网络
                        </div>
                    </div>
                </section>

                <section class="section">
                    <h2 class="section-title">
                        <i class="fas fa-project-diagram"></i>
                        项目经验
                    </h2>
                    <div class="experience-item">
                        <div class="item-header">
                            <div>
                                <div class="item-title">宗申摩托-国际智慧营销平台</div>
                                <div class="item-company">技术栈：Spring Boot, Vue.js, MySQL, Redis, Docker</div>
                            </div>
                            <div class="item-date">2024.04 - 2025.06</div>
                        </div>
                        <div class="item-description">
                            • 负责宗申摩托国际智慧营销平台的核心功能开发，支撑全球经销商业务<br>
                            • 使用Spring Boot构建微服务架构，实现用户管理、订单处理、库存管理等核心模块<br>
                            • 前端采用Vue.js开发响应式界面，提供良好的用户体验<br>
                            • 集成Redis缓存优化查询性能，使用MySQL进行数据持久化<br>
                            • 通过Docker容器化部署，提高系统的可维护性和扩展性<br>
                            • 系统支持多语言、多币种，服务于全球20+个国家的经销商网络
                        </div>
                    </div>
                    <div class="experience-item">
                        <div class="item-header">
                            <div>
                                <div class="item-title">上海世博会预约看房项目</div>
                                <div class="item-company">技术栈：SpringBoot + SpringSecurity + Tomcat + MyBatis + Redis</div>
                            </div>
                            <div class="item-date">2021.2 - 2021.5</div>
                        </div>
                        <div class="item-description">
                            • <strong>项目描述：</strong>整套预约购房流程，从用户预约看房到礼宾客服服务，到后台管理验资<br>
                            • <strong>实现功能：</strong>基于SpringSecurity的后台权限管理管理，用户安全认证包括后台和小程序用户，用户预约看房，用户资产资料审核，商品功能：包括商品管理和用户下单等功能，微信小程序开发（使用uniapp开发小程序）<br>
                            • <strong>核心技术：</strong>使用SpringSecurity实现细粒度权限控制，Redis缓存提升系统性能<br>
                            • <strong>项目成果：</strong>成功支撑上海世博会期间的房产预约业务，用户体验良好，系统稳定运行
                        </div>
                    </div>
                    <div class="experience-item">
                        <div class="item-header">
                            <div>
                                <div class="item-title">满集网广告系统</div>
                                <div class="item-company">技术栈：SpringCloud + SpringBoot + MyBatis + MySQL + Redis + RabbitMQ</div>
                            </div>
                            <div class="item-date">2020.6 - 2021.2</div>
                        </div>
                        <div class="item-description">
                            • <strong>项目描述：</strong>负责整个平台用户端广告购买和展示系统的设计与开发<br>
                            • <strong>实现功能：</strong>广告布局、广告模板编辑、广告购买、广告展示、广告素材审核和管理等核心功能<br>
                            • <strong>技术架构：</strong>采用SpringCloud微服务架构，实现服务间解耦和高可用性<br>
                            • <strong>性能优化：</strong>使用Redis缓存热点广告数据，RabbitMQ处理异步消息队列<br>
                            • <strong>项目成果：</strong>系统稳定支撑平台广告业务，提升用户广告投放效率和展示效果
                        </div>
                    </div>
                    <div class="experience-item">
                        <div class="item-header">
                            <div>
                                <div class="item-title">满集网信用信誉管理系统</div>
                                <div class="item-company">技术栈：SpringCloud + SpringBoot + MyBatis + MySQL + Redis + RabbitMQ</div>
                            </div>
                            <div class="item-date">2020.6 - 2021.2</div>
                        </div>
                        <div class="item-description">
                            • <strong>项目描述：</strong>负责满集网商家的信用信誉分评估和管理系统<br>
                            • <strong>实现功能：</strong>商家违规后触发相应扣分操作，分数达到系统配置的相应分数后，会对商家进行相应的处罚，扣分规则实现数据库配置<br>
                            • <strong>核心技术：</strong>基于SpringCloud微服务架构，实现灵活的规则配置和自动化处罚机制<br>
                            • <strong>业务价值：</strong>建立完善的商家信用体系，提升平台整体服务质量和用户信任度<br>
                            • <strong>技术亮点：</strong>规则引擎设计，支持动态配置违规处罚规则，提高系统灵活性
                        </div>
                    </div>
                    <div class="experience-item">
                        <div class="item-header">
                            <div>
                                <div class="item-title">满集网客服系统</div>
                                <div class="item-company">技术栈：SpringCloud + SpringBoot + MyBatis + MySQL + Redis + RabbitMQ + WebSocket</div>
                            </div>
                            <div class="item-date">2019.4 - 2021.2</div>
                        </div>
                        <div class="item-description">
                            • <strong>项目描述：</strong>负责平台用户资料的检查和审核，提供全方位的客服支持<br>
                            • <strong>在线聊天系统：</strong>平台客服在线聊天，使用WebSocket开发的实时聊天系统，支持文字、图片、文件等多媒体消息<br>
                            • <strong>审核系统：</strong>系统各种数据的审核，协调各个微服务间的数据有效性，确保平台数据质量<br>
                            • <strong>短信模板管理：</strong>发送节日祝福短信，营销推广等自动化消息服务<br>
                            • <strong>客服事务：</strong>负责平台商家信息管理、商品信息管理、平台购物订单管理、人事管理等客服人员排班管理<br>
                            • <strong>技术亮点：</strong>WebSocket实时通信，微服务数据一致性保障，自动化工作流程
                        </div>
                    </div>
                    <div class="experience-item">
                        <div class="item-header">
                            <div>
                                <div class="item-title">学学车（APP接口）</div>
                                <div class="item-company">技术栈：Tomcat + SpringBoot + MyBatis + MySQL + Shiro + Redis + JWT</div>
                            </div>
                            <div class="item-date">2018.6 - 2019.4</div>
                        </div>
                        <div class="item-description">
                            • <strong>项目描述：</strong>简化驾校报名流程，整合驾校人员管理，统一学车规范的移动应用系统<br>
                            • <strong>管理端功能：</strong>登录注册、包括第三方登录（微信/QQ），驾校套餐管理、驾校考勤地点设置、驾校考勤时间设置、人员排班、驾校组织架构管理、驾校人员管理、教练车管理、教练管理、驾校费用设置（按照区域设置费用）、学员管理、录入报名学员、学员报考（报考批次设置，统一提交车管所）、考勤打卡、考勤管理、请假/外出、预约学车（预约后准时到达指定地址即可）、预约管理、学车培训进度/流程、支付（微信/支付宝）、驾校财务结算、驾校员工的时监管（时时位置查看）、财务汇报、数据导出、流程设置（比如请假流程，支付申请流程）、流程审核、报销申请、付款申请<br>
                            • <strong>学员端功能：</strong>预约学车、网上报名、钱包功能<br>
                            • <strong>技术亮点：</strong>Shiro安全框架，JWT无状态认证，Redis缓存优化，第三方支付集成
                        </div>
                    </div>
                    <div class="experience-item">
                        <div class="item-header">
                            <div>
                                <div class="item-title">帮帮乐后台管理系统</div>
                                <div class="item-company">技术栈：Tomcat + SpringMVC + MyBatis + MySQL + Shiro + Redis + Bootstrap</div>
                            </div>
                            <div class="item-date">2018.1 - 2018.6</div>
                        </div>
                        <div class="item-description">
                            • <strong>项目描述：</strong>管理APP产生的订单，举报投诉审核，收益统计的综合管理平台<br>
                            • <strong>核心功能：</strong>用户权限管理、按钮级别细颗粒度控制、用户角色管理、兼职任务管理系统（任务审核、任务进度、任务异常处理）、用户投诉举报审核、乐豆管理、支付收益统计、广告设置<br>
                            • <strong>管理特色：</strong>多级权限控制，任务全生命周期管理，用户行为监控<br>
                            • <strong>技术亮点：</strong>SpringMVC + Shiro安全框架，Redis缓存优化，Bootstrap响应式设计<br>
                            • <strong>业务价值：</strong>为兼职平台提供完整的后台管理解决方案，支持任务发布、审核、结算全流程
                        </div>
                    </div>
                    <div class="experience-item">
                        <div class="item-header">
                            <div>
                                <div class="item-title">帮帮乐兼职平台</div>
                                <div class="item-company">技术栈：Tomcat + SpringMVC + MyBatis + MySQL + Redis</div>
                            </div>
                            <div class="item-date">2017.10 - 2018.1</div>
                        </div>
                        <div class="item-description">
                            • <strong>项目描述：</strong>帮助外出务工人员在业余时间兼职的移动应用平台<br>
                            • <strong>核心功能：</strong>兼职任务发布、兼职任务接单、兼职任务异常处理（人工/在线处理）、订单管理、乐豆（虚拟资产）充值、（微信/支付宝/钱包支付）、广告投放、举报投诉<br>
                            • <strong>平台特色：</strong>灵活的任务发布机制，多样化的支付方式，完善的投诉处理流程<br>
                            • <strong>技术亮点：</strong>SpringMVC架构，Redis缓存，第三方支付集成，移动端API设计<br>
                            • <strong>社会价值：</strong>为务工人员提供额外收入渠道，促进灵活就业和社会资源优化配置
                        </div>
                    </div>
                    <div class="experience-item">
                        <div class="item-header">
                            <div>
                                <div class="item-title">玉门零距离后台管理系统</div>
                                <div class="item-company">技术栈：Tomcat + SpringMVC + MyBatis + MySQL + Shiro + Redis + Bootstrap</div>
                            </div>
                            <div class="item-date">2017.8 - 2017.10</div>
                        </div>
                        <div class="item-description">
                            • <strong>项目描述：</strong>负责app产生的订单和公交信息等管理的后台管理系统<br>
                            • <strong>系统功能：</strong>系统权限管理、按钮级别细度控制、用户角色管理、不同角色不同权限、打车司机管理、包括司机资料修改、查看、审核、时时位置、强制下线、订单管理、修改订单状态、订单进度、订单联络、用户管理、用户实名信息审核、公交车时时位置查看、公交线路管理、新增线路和修改线路（新增站点/修改站点、线路经纬度微调、站点经纬度微调）、公交公司管理、公交公司车辆管理、公交车辆定位设备绑定/解绑<br>
                            • <strong>技术特色：</strong>SpringMVC架构，Shiro权限控制，Bootstrap响应式界面，实时位置监控<br>
                            • <strong>核心价值：</strong>提供完整的出行服务管理解决方案，支持打车和公交双重业务模式
                        </div>
                    </div>
                    <div class="experience-item">
                        <div class="item-header">
                            <div>
                                <div class="item-title">玉门零距离打车/公交车系统</div>
                                <div class="item-company">技术栈：Tomcat + SpringMVC + MyBatis + MySQL + Redis</div>
                            </div>
                            <div class="item-date">2017.3 - 2017.8</div>
                        </div>
                        <div class="item-description">
                            • <strong>项目描述：</strong>类似滴滴打车系统，为用户出行提供便利；公交车模块可实时查看公交车位置和到站情况，大冬天再也不用在冷风中等车，项目使用高德导航系统<br>
                            • <strong>打车功能：</strong>公交司机端实时上传位置信息，时时检测到站情况；司机端实时上传位置信息，时时查询附近打车订单，司机接单，司机订单系统<br>
                            • <strong>用户功能：</strong>用户端时时显示附近司机数量，及时下单或者预约下单，下单自动放入待接单队列，用户订单系统，支付（微信/支付宝），公交车模块可时时查看公交车位置和到站情况<br>
                            • <strong>技术亮点：</strong>高德地图API集成，实时位置追踪，订单队列管理，第三方支付接入<br>
                            • <strong>业务价值：</strong>解决用户出行痛点，提供便捷的打车和公交查询服务
                        </div>
                    </div>
                    
                    <div class="experience-item">
                        <div class="item-header">
                            <div>
                                <div class="item-title">分布式电商平台</div>
                                <div class="item-company">技术栈：Spring Cloud, MySQL, Redis, RabbitMQ</div>
                            </div>
                            <div class="item-date">2022.01 - 2022.12</div>
                        </div>
                        <div class="item-description">
                            • 设计并实现了支持高并发的分布式电商系统<br>
                            • 使用Spring Cloud Gateway作为API网关，实现统一鉴权和限流<br>
                            • 采用Redis缓存热点数据，RabbitMQ处理异步消息<br>
                            • 系统支持日均100万订单处理，峰值QPS达到5000+
                        </div>
                    </div>
                </section>

                <section class="section">
                    <h2 class="section-title">
                        <i class="fas fa-star"></i>
                        我的优势
                    </h2>
                    <div class="advantages-grid">
                        <div class="advantage-item">
                            <div class="advantage-icon">
                                <i class="fas fa-rocket"></i>
                            </div>
                            <div class="advantage-content">
                                <h3>技术深度</h3>
                                <p>深入理解Java底层原理，熟悉JVM调优，具备扎实的计算机基础知识</p>
                            </div>
                        </div>
                        <div class="advantage-item">
                            <div class="advantage-icon">
                                <i class="fas fa-users"></i>
                            </div>
                            <div class="advantage-content">
                                <h3>团队协作</h3>
                                <p>积极配合成员工作，有效沟通、合理分工、关注进度，确保团队目标达成。</p>
                            </div>
                        </div>
                        <div class="advantage-item">
                            <div class="advantage-icon">
                                <i class="fas fa-lightbulb"></i>
                            </div>
                            <div class="advantage-content">
                                <h3>问题解决</h3>
                                <p>善于分析复杂问题，能够快速定位并解决线上故障，保障系统稳定运行</p>
                            </div>
                        </div>
                        <div class="advantage-item">
                            <div class="advantage-icon">
                                <i class="fas fa-chart-line"></i>
                            </div>
                            <div class="advantage-content">
                                <h3>性能优化</h3>
                                <p>擅长系统性能调优，有丰富的高并发场景处理经验</p>
                            </div>
                        </div>
                    </div>
                </section>
        </div>
    </div>
</body>
</html>
